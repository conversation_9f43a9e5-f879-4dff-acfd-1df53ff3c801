stages:
  - release
  - build
  - deploy

# 设置相关变量
variables:
  BuildDir: ./build
  ServerDir: /bfdx_server
  DeployDir: $ServerDir/webclient
  PkgpushGit: https://pkgpush:$<EMAIL>/bfdx/bf8100pkg.git
  PkgBranch: bf8100_web
  Bf8100pkgFiles: https://git.kicad99.com/api/v4/projects/118/repository/files

cache:
  key: $CI_PROJECT_NAME.$CI_COMMIT_REF_NAME
  paths:
    - .pnpm-store

.env_script:
  before_script:
    - npm install --global corepack@latest
    - corepack enable
    - corepack prepare pnpm@latest-10 --activate
    - pnpm config set store-dir .pnpm-store
    - pnpm config set registry https://registry.npmmirror.com/
    - pnpm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
    - pnpm config set disturl https://npmmirror.com/mirrors/node/
    - pnpm config set sharp_binary_host "https://npmmirror.com/mirrors/sharp"
    - pnpm config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips"

# 自动添加tag和发布release
release:
  stage: release
  image: node:20-bullseye
  extends: .env_script
  script:
    - pnpm install
    - npx semantic-release
  only:
    - 292-8100-ui-v3
  tags:
    - docker
  except:
    - api
  artifacts:
    expire_in: 3 day
    paths:
      - package.json
  retry: 1

deploy-build:
  extends: .env_script
  image: node:20-bullseye
  stage: build
  tags:
    - docker
  only:
    - 292-8100-ui-v3
    - web
  script:
    - rm -rf $BuildDir
    # 先设置vue脚手架的环境配置$Enable_License 从GitLab CI上触发Pipeline时设置，默认为true，以开启授权验证
    - ./enableLicense.sh $Enable_License
    - pnpm install
    - pnpm run build
  dependencies:
    - release
  artifacts:
    expire_in: 60 day
    name: 'webclient'
    paths:
      - $BuildDir
      - package.json
  retry: 1

deploy-web:
  # extends: .env_script
  image: node:20-bullseye
  stage: deploy
  tags:
    - docker
  rules:
    - if: $CI_COMMIT_BRANCH != "292-8100-ui-v3"
      when: never
    - if: $Enable_License == "true"
      when: always
    - when: manual
  script:
    - export version=$(node -p "require('./package.json').version")
    - echo "deploy web version:$version, $(date +"%Y-%m-%d %H:%M:%S")"
    - '[ ! -d $DeployDir ] && mkdir -p $DeployDir && chmod 777 $DeployDir'
    - cp -r -u $BuildDir/webclient-$version/* $DeployDir/
    - node ./clearDeployCache.cjs -s $BuildDir/webclient-$version/ -t $DeployDir/
  dependencies:
    - deploy-build

pkg-push:
  image: node:20-bullseye
  stage: deploy
  tags:
    - docker
  rules:
    - if: $CI_COMMIT_BRANCH != "292-8100-ui-v3"
      when: never
    - if: $Enable_License == "true"
      when: always
    - when: manual
  dependencies:
    - deploy-build
  script:
    - export version=$(node -p "require('./package.json').version")
    - echo "$PkgBranch version:\ $version"
    - cd $BuildDir && mkdir -p $PkgBranch && mv webclient-$version $PkgBranch/$version
    - ls -hl $PkgBranch
    - if [[ $CI_COMMIT_TITLE == *"release"* ]]; then git init && git config user.name "linfl" && git config user.email "<EMAIL>" &&  git remote add origin $PkgpushGit && git branch -m $PkgBranch &&  curl --header "PRIVATE-TOKEN:$automr" "$Bf8100pkgFiles/.gitlab-ci.yml/raw?ref=main" -o .gitlab-ci.yml   && git add . && git commit -m "$version" && git push -f origin $PkgBranch ; fi
  cache:
    key: $CI_PROJECT_NAME.$CI_COMMIT_REF_NAME
    paths:
      - .pnpm-store
## 新版UI编译
#build-new-ui:
#  extends: .env_script
#  image: node:20-bullseye
#  stage: build
#  tags:
#    - docker
#  only:
#    - 173-8100-ui
#  script:
#    - rm -rf $BuildDir
#    # 先设置vue脚手架的环境配置$Enable_License 从GitLab CI上触发Pipeline时设置，默认为true，以开启授权验证
#    - ./enableLicense.sh $Enable_License
#    - pnpm install
#    - pnpm run build
#  artifacts:
#    expire_in: 60 day
#    name: new_ui
#    paths:
#      - $BuildDir
#      - package.json
#  retry: 1
#  dependencies:
#    - release
#
## 新版UI部署
#deploy-new-ui:
#  # extends: .env_script
#  image: node:20-bullseye
#  stage: deploy
#  tags:
#    - docker
#  rules:
#    - if: $CI_COMMIT_BRANCH != "173-8100-ui"
#      when: never
#    - when: always
#  script:
#    - export version=$(node -p "require('./package.json').version")
#    - echo "deploy web version:$version"
#    - rm -rf $DeployDir/new-ui && cp -r -u $BuildDir/webclient-$version $DeployDir/new-ui
#  dependencies:
#    - build-new-ui
