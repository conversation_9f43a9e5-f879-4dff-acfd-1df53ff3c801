<script setup lang="ts">
  import { useI18n } from 'vue-i18n'
  import { convertPxToRemWithUnit, calcScaleSize } from '@/utils/setRem'
  import { onBeforeUnmount, onMounted } from 'vue'
  import { addRecentContactFromHistory } from '@/utils/callContact'

  const { t } = useI18n()
  const leftAsideWidth = convertPxToRemWithUnit(calcScaleSize(282))

  // todo: handle events
  const handleLocate = (targetDmrId: string) => {
    console.log('Locating:', targetDmrId)
  }

  const handleCall = (targetDmrId: string) => {
    console.log('Calling:', targetDmrId)
  }

  const handleHangup = (targetDmrId: string) => {
    console.log('Hanging up:', targetDmrId)
  }

  const handleMessage = (targetDmrId: string) => {
    console.log('Messaging:', targetDmrId)
  }

  const handleSendCommand = (targetDmrId: string) => {
    console.log('Sending command to:', targetDmrId)
  }

  const handleSendMessage = (targetDmrId: string) => {
    console.log('Sending message to:', targetDmrId)
  }

  const handleAddToCommonContact = (targetDmrId: string) => {
    console.log('Adding to common contacts:', targetDmrId)
  }

  const handleRemoveFromCommonContact = (targetDmrId: string) => {
    console.log('Removing from common contacts:', targetDmrId)
  }

  onMounted(() => {
    bfglob.on('db_sound_history', addRecentContactFromHistory)
  })

  onBeforeUnmount(() => {
    bfglob.off('db_sound_history', addRecentContactFromHistory)
  })
</script>

<template>
  <el-container class="flex-auto max-h-[100vh_-_146.5px] overflow-y-auto !px-[40px] !pb-[47px] !p-0 text-white">
    <el-aside class="flex flex-col gap-[15px]" :width="leftAsideWidth">
      <PageHeader :title="t('dispatch.functionList.name')" />
      <DispatchFunctionList />
    </el-aside>
    <el-main class="px-[15px]! py-0! flex! h-full flex-col gap-[15px]">
      <el-row class="h-[58%] flex-col flex-nowrap! gap-[7px]">
        <DispatchCommonContact
          @locate="handleLocate"
          @call="handleCall"
          @hangup="handleHangup"
          @message="handleMessage"
          @send-command="handleSendCommand"
          @send-message="handleSendMessage"
        />
      </el-row>
      <el-row :gutter="15" class="h-[42%] flex-nowrap!">
        <el-col :span="12" class="flex! flex-col flex-nowrap gap-[7px]">
          <DispatchRecentContact @call="handleCall" @add-to-common="handleAddToCommonContact" @remove-from-common="handleRemoveFromCommonContact" />
        </el-col>
        <el-col :span="12" class="flex! flex-col flex-nowrap gap-[7px]">
          <DispatchDynamicContact
            @locate="handleLocate"
            @call="handleCall"
            @hangup="handleHangup"
            @message="handleMessage"
            @send-command="handleSendCommand"
            @send-message="handleSendMessage"
          />
        </el-col>
      </el-row>
    </el-main>
    <DispatchTree class="w-[350px]! h-full" :enable-checkbox="false" />
  </el-container>
</template>

<style lang="scss" scoped></style>
