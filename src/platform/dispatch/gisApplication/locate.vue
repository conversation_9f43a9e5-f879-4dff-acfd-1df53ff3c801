<template>
  <div class="locate-container">
    <!-- 定位监控按钮 -->
    <bf-button color-type="primary" @click="openLocateDialog">
      {{ $t('dialog.locateCtrl') }}
    </bf-button>

    <!-- 定位监控弹窗 -->
    <bf-dialog
      v-model="dialogVisible"
      :title="$t('dialog.locateCtrl')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal="false"
      class="header-border shadow-md shadow-slate-800 locate-dialog drag-dialog"
      modal-class="drag-dialog-modal"
      append-to-body
      draggable
      center
      @open="openDlgFn"
    >
      <!-- 定位监控表单 -->
      <el-form v-if="true" :model="cb01Cmd" label-position="left" class="cb01 w-[400px]">
        <el-form-item>
          <template #label>
            <EllipsisText :content="$t('dialog.locateCount') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
          </template>
          <bf-input-numberV2 v-model="cb01Cmd.count" :min="1" :max="9999" class="!w-full" style="height: 50px" />
        </el-form-item>

        <el-form-item>
          <template #label>
            <EllipsisText :content="$t('dialog.locateSpacing') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
          </template>
          <bf-input-numberV2 v-model="cb01Cmd.spaceTime" :min="5" :max="9995" :step="5" class="!w-full" style="height: 50px" />
        </el-form-item>

        <el-form-item>
          <template #label>
            <EllipsisText :content="$t('dialog.size') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
          </template>
          <bf-input-numberV2 v-model="cb01Cmd.size" :min="0" :max="495" :step="5" class="!w-full" style="height: 50px" />
        </el-form-item>

        <div class="flex justify-center">
          <bf-button color-type="primary" :disabled="sendCmdBtn" @click="send_cb01_cmd">
            {{ $t('dialog.sendCmdTitle') }}
          </bf-button>
        </div>
      </el-form>
    </bf-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { useI18n } from 'vue-i18n'
  import bfDialog from '@/components/bfDialog/main'
  import bfButton from '@/components/bfButton/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import bfNotify from '@/utils/notify'
  import bfprocess from '@/utils/bfprocess'

  // Props 定义
  interface Props {
    dmrid?: string | number
  }

  const props = withDefaults(defineProps<Props>(), {
    dmrid: '',
  })

  // 国际化
  const { t } = useI18n()

  // 响应式数据
  const dialogVisible = ref(false)
  const sendCmdBtn = ref(false)

  // cb01 命令数据
  const cb01Cmd = reactive({
    count: 1,
    spaceTime: 5,
    size: 20,
  })

  // 方法
  const openDlgFn = () => {
    // 发布关闭最小化导航区对应按钮事件
    // 这里可以根据需要添加相关逻辑
  }

  const openLocateDialog = () => {
    dialogVisible.value = true
    openDlgFn()
  }

  // 发送 cb01 定位监控命令
  const send_cb01_cmd = () => {
    // 构建目标对象
    const cmdTarget = {
      groud: [],
      device: props.dmrid ? [props.dmrid] : [],
    }

    // 检查是否有目标
    if (cmdTarget.groud.length === 0 && cmdTarget.device.length === 0) {
      bfNotify.warningBox(t('msgbox.selectTarget'), 'error')
      return
    }

    // 数据处理，确保是5的倍数
    const _size = Math.round(cb01Cmd.size / 5) * 5
    const _spaceTime = Math.round(cb01Cmd.spaceTime / 5) * 5
    cb01Cmd.size = _size
    cb01Cmd.spaceTime = _spaceTime

    // 发送命令
    bfprocess.cb01(cmdTarget, cb01Cmd)

    // 禁用按钮3秒
    disabledSendCmdFunc()
  }

  // 发送命令后禁用按钮
  const disabledSendCmdFunc = (sec = 3000) => {
    sendCmdBtn.value = true
    setTimeout(() => {
      sendCmdBtn.value = false
    }, sec)
  }
</script>

<style lang="scss">
  .locate-container {
    padding: 20px;
  }

  .locate-dialog {
    .cb01 {
      .el-form-item {
        margin-bottom: 20px;
      }
    }
  }
</style>
